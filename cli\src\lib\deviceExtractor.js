import { fetchEudamedData, mapDeviceSummary } from './eudamedApi.js';
import cliProgress from 'cli-progress';

class DeviceExtractor {
  constructor(options = {}) {
    this.pageSize = options.pageSize || 300;
    this.concurrencyLimit = options.concurrencyLimit || 10;
    this.language = options.language || 'en';
    this.progressBar = null;
  }

  async getTotalPages() {
    console.log('Making initial request to determine total pages...');
    const initialParams = {
      page: '0',
      size: this.pageSize.toString(),
    };

    const { data: initialData, error: initialError } = await fetchEudamedData(
      '/api/devices/udiDiData', 
      initialParams, 
      this.language
    );

    if (initialError) {
      throw new Error(`Failed to get initial data: ${initialError.message}`);
    }

    if (!initialData || typeof initialData.totalPages !== 'number') {
      throw new Error('Invalid response: missing totalPages information');
    }

    const totalPages = initialData.totalPages;
    const totalElements = initialData.totalElements;

    console.log(`Total pages: ${totalPages}`);
    console.log(`Total elements: ${totalElements}`);
    console.log(`Page size: ${this.pageSize}`);

    return { totalPages, totalElements };
  }

  async processBatch(pageNumbers, database) {
    const batchSize = this.concurrencyLimit;
    const results = [];
    let totalSavedRecords = 0;

    // Initialize progress bar
    this.progressBar = new cliProgress.SingleBar({
      format: 'Progress |{bar}| {percentage}% | {value}/{total} pages | Saved: {savedRecords} | Batch: {currentBatch}/{totalBatches}',
      barCompleteChar: '\u2588',
      barIncompleteChar: '\u2591',
      hideCursor: true
    });

    this.progressBar.start(pageNumbers.length, 0, {
      savedRecords: 0,
      currentBatch: 0,
      totalBatches: Math.ceil(pageNumbers.length / batchSize)
    });

    // Process pages in batches with concurrency control
    for (let i = 0; i < pageNumbers.length; i += batchSize) {
      const batch = pageNumbers.slice(i, i + batchSize);

      const batchPromises = batch.map(async (pageNumber) => {
        try {
          const eudamedParams = {
            page: pageNumber.toString(),
            size: this.pageSize.toString(),
          };

          const { data, error } = await fetchEudamedData('/api/devices/udiDiData', eudamedParams, this.language);

          if (error) {
            console.error(`Error fetching page ${pageNumber}:`, error);
            return { pageNumber, error, data: null, savedCount: 0 };
          }

          // Map devices using mapDeviceSummary function
          let mappedDevices = [];
          if (data.content && Array.isArray(data.content)) {
            mappedDevices = data.content
              .filter(device => device && device.primaryDi) // Filter out devices without primaryDi
              .map(device => mapDeviceSummary(device))
              .filter(device => device !== null); // Filter out null results from mapping
          }

          // Save devices to database
          let savedCount = 0;
          if (mappedDevices.length > 0) {
            savedCount = database.saveDevices(mappedDevices);
          }

          return {
            pageNumber,
            error: null,
            data: {
              totalElements: data.totalElements,
              totalPages: data.totalPages,
              size: data.size,
              number: data.number,
              deviceCount: mappedDevices.length
            },
            savedCount
          };

        } catch (error) {
          console.error(`Unexpected error processing page ${pageNumber}:`, error);
          return { pageNumber, error: error.message, data: null, savedCount: 0 };
        }
      });

      // Wait for all promises in the current batch to complete
      const batchResults = await Promise.all(batchPromises);

      // Update total saved records
      totalSavedRecords += batchResults.reduce((sum, result) => sum + result.savedCount, 0);

      results.push(...batchResults);

      // Update progress bar
      if (this.progressBar) {
        this.progressBar.update(results.length, {
          savedRecords: totalSavedRecords,
          currentBatch: Math.floor(i / batchSize) + 1,
          totalBatches: Math.ceil(pageNumbers.length / batchSize)
        });
      }
    }

    return { results, totalSavedRecords };
  }

  async extractDevices(database, options = {}) {
    const { startPage = 0, endPage = null, pageRange = null } = options;

    try {
      // Get total pages
      const { totalPages, totalElements } = await this.getTotalPages();

      // Determine which pages to fetch
      let pageNumbers;
      if (pageRange) {
        // Parse page range (e.g., "0-10,15,20-25")
        pageNumbers = this.parsePageRange(pageRange, totalPages);
      } else if (endPage !== null) {
        // Use start and end page
        const actualEndPage = Math.min(endPage, totalPages - 1);
        pageNumbers = Array.from({ length: actualEndPage - startPage + 1 }, (_, i) => startPage + i);
      } else {
        // Use all pages from startPage to end
        pageNumbers = Array.from({ length: totalPages - startPage }, (_, i) => startPage + i);
      }

      console.log(`Processing ${pageNumbers.length} pages with concurrency limit of ${this.concurrencyLimit}...`);
      const startTime = Date.now();

      // Process all pages
      const { results: pageResults, totalSavedRecords } = await this.processBatch(pageNumbers, database);

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      // Stop progress bar
      if (this.progressBar) {
        this.progressBar.stop();
      }

      // Calculate statistics
      const successfulPages = pageResults.filter(result => !result.error).length;
      const failedPages = pageResults.filter(result => result.error).length;

      return {
        success: true,
        timestamp: new Date().toISOString(),
        processingTimeMs: processingTime,
        totalElements,
        totalPages,
        pagesProcessed: pageResults.length,
        successfulPages,
        failedPages,
        totalSavedRecords,
        pageResults: pageResults.filter(result => result.error) // Only return failed pages for debugging
      };

    } catch (error) {
      // Stop progress bar on error
      if (this.progressBar) {
        this.progressBar.stop();
      }
      throw error;
    }
  }

  parsePageRange(rangeStr, totalPages) {
    const pages = new Set();
    const parts = rangeStr.split(',');

    for (const part of parts) {
      const trimmed = part.trim();
      if (trimmed.includes('-')) {
        // Range like "0-10"
        const [start, end] = trimmed.split('-').map(n => parseInt(n.trim()));
        if (isNaN(start) || isNaN(end)) {
          throw new Error(`Invalid page range: ${trimmed}`);
        }
        for (let i = start; i <= Math.min(end, totalPages - 1); i++) {
          pages.add(i);
        }
      } else {
        // Single page
        const page = parseInt(trimmed);
        if (isNaN(page)) {
          throw new Error(`Invalid page number: ${trimmed}`);
        }
        if (page < totalPages) {
          pages.add(page);
        }
      }
    }

    return Array.from(pages).sort((a, b) => a - b);
  }
}

export { DeviceExtractor };
